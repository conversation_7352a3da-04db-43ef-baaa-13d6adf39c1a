<?php
//
// Block - Content Page Columns (Standard)
//

// ACF Fields
$title = $fields['title'];
$label = $fields['label'];
$text = $fields['text'];
$image = $fields['image'];
$link = $fields['link'];

// Rapporto colonne
$columnsLayout = $fields['columns_layout'];
$customLayout75 = $fields['columns_layout_image'];

if ($customLayout75) {
    $contentColClass = 'col-lg-5';
    $imageColClass = 'col-lg-7';
} else {
    $contentColClass = $columnsLayout ? 'col-lg-8' : 'col-lg-6';
    $imageColClass = $columnsLayout ? 'col-lg-4' : 'col-lg-6';
}

// Classe per invertire le colonne
$columns = $fields['columns'];
$rowClass = $columns ? 'flex-row-reverse' : '';

?>

<section class="block-content-page-columns-standard <?= render_options($options); ?>" <?= render_bg_color($options); ?>>
    <div class="<?= render_container($options); ?>">
        <div class="row align-items-center <?= $rowClass; ?>">
            <div class="<?= $contentColClass; ?>">
                
                <div class="block-content-page-columns-standard__content">
                    <?php if($label) : ?>
                        <span class="label font__body size__body-big"><?= $label ?></span>
                    <?php endif;?>
                    <?php if($title) : ?>
                        <h2 class="title font__display size__h1"><?= $title ?></h2>
                    <?php endif;?>
                    <?php if($text) : ?>
                        <div class="text"><?= $text ?></div>
                    <?php endif;?>
                    <?php if($link) : ?>
                        <a href="<?= esc_url($link['url']); ?>" class="link-cta">
                            <?= esc_html($link['title']); ?>
                            <?= get_svg('cta-arrow'); ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="<?= $imageColClass; ?>">
                <?php if ($image): ?>
                    <div class="container-image__relative">
                    <img 
                        class="image img-fluid"
                        src="<?= esc_url($image['url']); ?>" 
                        alt="<?= esc_attr($image['alt']); ?>" 
                        width="<?= esc_attr($image['width']); ?>" 
                        height="<?= esc_attr($image['height']); ?>">
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>