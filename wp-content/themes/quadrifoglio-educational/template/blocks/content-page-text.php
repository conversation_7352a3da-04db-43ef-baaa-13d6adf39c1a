<?php 
// Block Content Text for Pages
// ACF
$title = $fields['title'];
$text = $fields['text'];
$readMoreText = $fields['text_read_more'];
$ctas = $fields['ctas'];

// Testi traducibili con Polylang
$read_more_label = __('Leggi di più', 'quadrifoglio-educational');
$read_less_label = __('Leggi di meno', 'quadrifoglio-educational');
?>

<section class="block-content-page-text <?= render_options($options); ?>" <?= render_bg_color($options); ?>>
    <div class="<?= render_container($options); ?>">
        <div class="row">
            <div class="col-12">
                <?php if($title) : ?>
                    <h2 class="title size__h3"><?= $title ?></h2>
                <?php endif;?>
                <?php if($text) : ?>
                    <div class="block-content-page-text__content">
                        <?= $text ?>

                        <?php if($readMoreText) : ?>
                            <div class="read-more collapse" id="collapseReadMore">
                                <?= wp_kses_post($readMoreText); ?>
                            </div>
                            <span class="link-cta readmore-link" href="#collapseReadMore" data-text-readmore="<?= $read_more_label ?>" data-text-readless="<?= $read_less_label ?>" data-bs-toggle="collapse" href="#collapseReadMore" role="button" aria-expanded="false" aria-controls="collapseReadMore" href="#">
                                <?= $read_more_label ?>
                            </span>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
                <?php if($ctas) : ?>
                    <div class="block-content-page-text__ctas">
                        <?php foreach($ctas as $cta) : 
                            $link = $cta['link'];
                            $icon = $cta['icon'];
                            $linkStyle = $cta['link_style'];
                            $has_icon_class = ($icon === true) ? ' has-arrow' : '';
                            $has_style_class = ($linkStyle === true) ? ' underline' : '';
                            ?>
                            <a class="d-block button-cta font__body<?= $has_icon_class ?><?= $has_style_class ?>" href="<?= $link['url'] ?>" title="<?= $link['title'] ?>" target="<?= $link['target'] ?>">
                                <?php if($icon === true) : ?>
                                    <?php echo get_svg('arrow-button'); ?>
                                <?php endif;?>
                                <span><?= $link['title'] ?></span>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>