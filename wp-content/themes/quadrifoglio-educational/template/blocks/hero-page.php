<?php 
// Block Hero Page
// ACF
$bg_desktop = $fields['desktop_image'];
$bg_mobile = $fields['mobile_image'];
$title = $fields['title'];
$text = $fields['text'];
$horizontalAlign = $fields['horizontal_align'];
$verticalAlign = $fields['vertical_align'];
$showScroll = $fields['show_scroll'];

// Mappa valori in classi Bootstrap
$justifyClass = 'justify-content-' . $horizontalAlign;
$alignClass = 'align-items-' . $verticalAlign;
?>

<section class="block-hero-page <?= render_options($options); ?> d-flex <?= $justifyClass ?> <?= $alignClass ?>">
    <div class="container-image__absolute">
        <img class="image__absolute-default" src="<?= $bg_desktop['url']; ?>" alt="" class="image-absolute">
    </div>
    <div class="overlay-gradient"></div>
    <?php if($title || $text ) : ?>
        <div class="block-hero-page__content r-20">
            <?php if($title) : ?>
                <h1 class="title font-display size-h1"><?= $title ?></h1>
            <?php endif;?>
            <?php if($text) : ?>
                <div class="text"><?= $text ?></div>
            <?php endif;?>
        </div>
    <?php endif; ?>
    <?php if($showScroll) : ?>
        <div class="block-navbar__next-section">
            <div class="container-next-section">
                <div class="scroll">
                    <?php if(is_page('servizi') || is_page('services')) : ?>
                        <u><?= __("Tutti i nostri servizi", "quadrifoglio"); ?></u>
                    <?php else : ?>
                        <u>scroll</u>
                    <?php endif; ?>
                    <?= get_svg('scroll-down-icon'); ?>
                </div>
            </div>
        </div>
    <?php endif;?>
</section>