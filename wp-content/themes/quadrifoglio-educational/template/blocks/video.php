<?php 
// Block - Video
$videoSource = $fields['video_source'];
$videoLocalMp4 = $fields['local_video_mp4'];
$videoLocalWebm = $fields['local_video_webm'];
$videoPoster = $fields['local_video_poster'];
$videoEmbed = $fields['embed_video'];
?>

<section class="block-video <?= render_options($options); ?>">
    <div class="container-fluid">
        <div class="row">
            <?php if ($videoSource): ?>
                <!-- Video Embed -->
                <div class="video-embed">
                    <?php
                        preg_match('/src="(.+?)"/', $videoEmbed, $matches);
                        $src = $matches[1];

                        // Imposta parametri in base al dominio
                        if (strpos($src, 'youtube.com') !== false) {
                            $params = array(
                                'autoplay'     => 1,
                                'mute'         => 1,
                                'controls'     => 0,
                                'hd'           => 1,
                                'autohide'     => 1,
                                'playsinline'  => 1
                            );
                        } elseif (strpos($src, 'vimeo.com') !== false) {
                            $params = array(
                                'autoplay'     => 1,
                                'muted'        => 1,
                                'background'   => 1,
                                'title'        => 0,
                                'byline'       => 0,
                                'portrait'     => 0,
                                'playsinline'  => 1
                            );
                        } else {
                            $params = array(); // fallback
                        }

                        $new_src = add_query_arg($params, $src);
                        $videoEmbed = str_replace($src, $new_src, $videoEmbed);

                        // Aggiungi attributi all'iframe
                        $attributes = 'frameborder="0" allow="autoplay; fullscreen" allowfullscreen loading="lazy"';
                        $videoEmbed = str_replace('></iframe>', ' ' . $attributes . '></iframe>', $videoEmbed);

                        echo $videoEmbed;
                    ?>
                </div>
            <?php else: ?>
                <!-- Video Locale -->
                <video autoplay muted loop playsinline poster="<?= esc_url($videoPoster['url']); ?>">
                    <?php if ($videoLocalMp4): ?>
                        <source src="<?= esc_url($videoLocalMp4); ?>" type="video/mp4">
                    <?php endif; ?>
                    <?php if ($videoLocalWebm): ?>
                        <source src="<?= esc_url($videoLocalWebm); ?>" type="video/webm">
                    <?php endif; ?>
                    Your browser does not support the video tag.
                </video>
            <?php endif; ?>
        </div>
    </div>
</section>
