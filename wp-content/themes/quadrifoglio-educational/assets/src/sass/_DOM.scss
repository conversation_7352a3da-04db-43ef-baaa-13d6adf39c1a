//
//  DOM
//

html,
body {
    font-family: $font-body;
    box-sizing: border-box;
    font-size: $size-font-base;
    overflow-x: hidden;

    &.logged-in {
        margin-top: 32px;
    }
}

.container-fluid {
    padding-left: rem(15px);
    padding-right: rem(15px);

    @include media-breakpoint-up(md) {
        padding-left: rem(20px);
        padding-right: rem(20px);
    }

    @include media-breakpoint-up(lg) {
        padding-left: rem(30px);
        padding-right: rem(30px);
    }

    @include media-breakpoint-up(xl) {
        padding-left: rem(40px);
        padding-right: rem(40px);
    }
}

p {
    margin-bottom: rem(24px);
}

// Fonts Type
.font__display {
    font-family: $font-display;
    font-weight: 700;
}

.font__body {
    font-family: $font-body;
    font-weight: 400;
}

// Fonts sizes
.size__h1 {
    font-size: clamp(48px, 2.917vw, 56px);
    line-height: clamp(48px, 2.917vw, 56px);
    padding-bottom: rem(16px);
}

.size__h2 {
    font-size: clamp(40px, 2.5vw, 48px);
    line-height: clamp(40px, 2.5vw, 48px);
}

.size__h3 {
    font-size: clamp(24px, 1.67vw, 32px);
    line-height: clamp(26px, 1.82vw, 35px);
}

.size__h4 {
    font-size: clamp(24px, 1.25vw, 24px);
    line-height: clamp(26px, 1.82vw, 26px);
}

.size__body-big {
    font-size: clamp(18px, 1.563vw, 20px);
    line-height: clamp(24px, 1.35vw, 28px);
}

.size-body-default {
    font-size: 16px;
    line-height: 24px;
}

.overlay-gradient {
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.2145) 88.22%, rgba(0, 0, 0, 0.3) 100%);
}
.overlay {
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
}


// Dropdown
.dropdown-menu {
    animation: 0.5s slideup;
}

@keyframes slideup {
    from {
        transform: translateY(10%);
    }

    to {
        transform: translateY(0);
    }
}
 
/* Animazione per schermi lg e inferiori */
@include media-breakpoint-down(xl) {
    .dropdown-menu:not(.lang-menu) {
        display: block !important; /* Evita il problema del display: none */
        visibility: hidden;
        max-height: 0;
        overflow: hidden;
        opacity: 0;
        transition: max-height 0.5s ease-in-out, opacity 0.5s ease-in-out, visibility 0.5s;
    }

    .dropdown-menu.show {
        visibility: visible;
        max-height: 500px; /* Imposta un valore grande a sufficienza */
        opacity: 1;
    }
}

.font-display {
    font-family: $font-display;
    font-weight: 700;
}

.size-body {
    font-size: clamp(18px, 1.563vw, 20px);
}

.size-h1 {
    font-size: clam(48px, 2.917vw, 56px);
}

.size-h3 {
    font-size: clam(24px, 1.67vw, 32px);
}
// Radius
.r-20 {
    border-radius: rem(20px);
}

// Stili per le immagini
.image__absolute-contain {
    @include absolute-full;
    object-fit: cover;
    object-position: contain;
}

// Contenitore di immagini assolute
.container-image__absolute,
.container-image__relative {
    overflow: hidden;
    width: 100%;
    height: 100%;
    
    .image__absolute-default {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
    }
}

.container-image__absolute {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.container-image__relative {
    position: relative;
}

.dynamic-container__image {
    transition: all 0.3s ease-in-out;
    &.section-hovered {
        border-radius: rem(50px);
    }

    @include media-breakpoint-down(lg) {
        border-radius: rem(50px);
    }
}


// Scroll Down Button
@keyframes scroll {
    from, to, 65% {transform: translate(-50%, 0)}
    25% {transform: translate(-50%, 60px)}
    26% {transform: translate(-50%, -60px)}
}
  
.container-next-section {
    height: 17px;
    position: absolute;
    bottom: rem(40px);
    right: rem(40px);
    z-index: 99;
    overflow: hidden;
    cursor: pointer;
    width: 100px;
    height: 40px;

    .scroll {
        color: $pure_white;
        margin: 12px 0;
        left: 50%;
        transform: translateX(-50%);
        text-transform: uppercase;
        font-weight: bold;
        position: absolute;
        top: 0px;
        display: flex;
        align-items: center;
        gap: rem(5px);
        animation: scroll 2.5s cubic-bezier(0.645,.045,.355,1) infinite;

        u {
            text-underline-offset: rem(5px);
        }
    }
}

// Stili per i link e pulsanti
.link-cta {
    display: inline-block;
    text-decoration: underline;
    text-transform: uppercase;
    color: $textColor;
    font-weight: 600;
    display: flex;
    gap: rem(10px);
    align-items: center;

    svg {
        transition: $default-transition;
    }

    &:hover {
        svg {
            transform: rotate(45deg);
        }
    }
}

.button-cta {
    text-transform: uppercase;
    text-decoration: none;
    color: $textColor;
    font-weight: 400;
    background-color: $pure_white;
    padding: rem(16px 24px);
    border: 1px solid $pure_black;
    font-size: rem(16px);
    line-height: rem(16px);
    transition: $default-transition;
    display: inline-block;

    &:hover {
        border-color: $pure_white;
        box-shadow: 0 5px 10px rgba(0,0,0,0.2);
    }

    &.button-cta-dark {
        background-color: $pure_black;
        color: $pure_white;

        &:hover {
            background-color: $pure_black;
        }
    }

    &.button-download {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: rem(5px);
        max-width: rem(220px);
        text-transform: none;

        &::before {
            content: '';
            width: rem(24px);
            height: rem(24px);
            background-image: url($path-svg + 'button-download-icon.svg');
        }
    }

    &.has-arrow {
        text-transform: none!important;
        display: flex!important;
        justify-content: center;
        align-items: center;
        gap: rem(8px);
        line-height: 100%;
        font-size: rem(16px);

        svg {
            path {
                transition: $default-transition;
            }
        }

        &:hover {
            background-color: $pure_black;
            color: $pure_white;

            svg {
                path {
                    fill: $pure_white;
                }
            }
        }

        &.underline {
            border: none!important;

            span {
                position: relative;
                display: inline-block;

                &::after {
                    content: '';
                    position: absolute;
                    left: 0;
                    bottom: rem(-3px); /* oppure usa -2px per spostarla più in basso */
                    width: 0%;
                    height: rem(1px);
                    background-color: currentColor; /* o un colore specifico */
                    transition: width 0.3s ease-in-out;
                }
            }

            &:hover {
                color: $pure_black;
                background-color: unset;
                box-shadow: unset;

                svg {
                    path {
                        fill: $pure_black;
                    }
                }

                span::after {
                    width: 100%;
                }
            }
        }
    }
}


//
//  Static Backgrounds Pages
//

.term-scuola-primaria-e-secondaria,
.term-infanzia {
    .block-product-child-ambienti {
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center top;
    }
}
.term-scuola-primaria-e-secondaria {
    .block-product-child-ambienti {
        background-image: url($path-svg + 'bg-scuola-primaria-secondaria.svg');

        @include media-breakpoint-down(sm) {
            background-image: url($path-svg + 'bg-scuola-primaria-secondaria-mobile.svg');
        }
    }
}
.term-infanzia {
    .block-product-child-ambienti {
        background-image: url($path-svg + 'bg-infanzia.svg');

        @include media-breakpoint-down(sm) {
            background-image: url($path-svg + 'bg-infanzia-mobile.svg');
        }
    }
}

// Read More testi wysiwyg
.js-readmore-container {

    .js-readmore-text {
        overflow: hidden;
        transition: max-height 0.4s ease;
    }

    .js-readmore-toggle {
        display: inline-block;
        text-decoration: underline;
        color: #2e2e2d;
        font-weight: 600;
        display: flex;
        gap: rem(10px);
        align-items: center;
        margin: rem(20px) auto;
        background-color: transparent;
        border: none;

        svg {
            transition: $default-transition;
        }

        &:hover {
            svg {
                transform: rotate(45deg);
            }
        }
    }
}
