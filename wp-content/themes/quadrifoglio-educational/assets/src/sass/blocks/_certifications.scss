$offcanvas-horizontal-width:        rem(640px);

.block-certifications {
    position: relative;

    .container-fluid {
        max-width: rem(1440px)!important;
        margin: 0 auto;
    }

    .title {
        color: $pure_white;
        text-align: center;
        margin-bottom: rem(30px);
    }

    &__content {
        max-width: rem(1220px);
        color: $pure_white;
        text-align: center;
        margin: 0 auto 40px;
    }

    &__certifications {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-column-gap: rem(16px);
        grid-row-gap: rem(32px);

        @include media-breakpoint-down(md) {
            grid-template-columns: repeat(2, 1fr);
        }

        .certification {
            display: flex;
            flex-direction: column;
            gap: rem(16px);
            align-items: center;
            justify-content: center;
            text-align: center;
            cursor: pointer;
            background-color: $pure_white;
            padding: rem(20px);
            transition: $default-transition;

            img {
                max-width: 100%;
                height: auto;
            }

            .name {
                font-size: rem(16px);
                line-height: rem(24px);
            }

            &:hover {
                border-radius: rem(20px);
            }
        }
    }

    .offcanvas {
        max-width: $offcanvas-horizontal-width;
        background-color: $pure_white;
        border: none;
        padding: rem(22px 50px);

        @include media-breakpoint-down(md) {
            max-width: 100%;
            padding: rem(15px);
        }

        .offcanvas-header,
        .offcanvas-body {
            padding: 0;
        }

        .offcanvas-header {
            padding-top: rem(7px);
            padding-bottom: rem(15px);
            margin-bottom: rem(5px);
            display: flex;
            justify-content: space-between;
            align-items: center;

            .offcanvas-title {
                font-size: rem(24px);
                line-height: rem(26px);
                font-weight: 700;
                letter-spacing: -1.5%;
            }

            .close-offcanvas {
                svg {
                    height: rem(20px);
                    width: rem(20px);
                }
            }
        }

        .offcanvas-body {
            padding-top: rem(12px);

            .slide-certification {
                display: flex!important;
                gap: rem(20px);
            }

            .slick-next,
            .slick-prev {
                width: auto!important;
                height: auto!important;
                top: unset;
                bottom: rem(-50px);
                z-index: 99999;
            }

            .slick-prev {
                left: rem(10px);

                @include media-breakpoint-down(md) {
                    left: 35%;
                }
            }

            .slick-next {
                right: rem(10px);
                @include media-breakpoint-down(md) {
                    right: 39%;
                }
            }

            .slick-dots {
                display: flex;
                justify-content: center;
                gap: rem(12px);
                bottom: rem(-30px);

                @include media-breakpoint-down(md) {
                    display: none;
                }

                li {
                    width: unset;
                    height: unset;
                    margin: 0;

                    &.slick-active {
                        button {
                            width: rem(30px);

                            &:before {
                                background-color: #AFA69E;
                                
                            }
                        }
                    }

                    button {
                        width: unset;
                        height: unset;
                        padding: 0;
                        width: rem(12px);
                        height: rem(12px);
                        transition: $default-transition;

                        &:before {
                            font-family: unset;
                            font-family: unset;
                            width: 100%;
                            height: 100%;
                            border-radius: rem(2px);
                            background-color: #2E2E2D;
                            opacity: 1;
                            color: unset;
                        }
                    }
                }
            }
        }
    }
}