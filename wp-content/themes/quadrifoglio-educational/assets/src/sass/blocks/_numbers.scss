.block-numbers {
    position: relative;

    &__content {
        padding-bottom: rem(40px);
        max-width: rem(912px);
        border-bottom: 1px solid rgba(46, 46, 45, 0.5);
        margin: 0 auto rem(40px);

        * {
            font-size: clamp(24px, 1.25vw, 24px);
            line-height: clamp(26px, 1.82vw, 26px);
            font-family: $font-display;
            margin-bottom: rem(0);
        }
    }

    &__numbers {
        max-width: rem(1440px);
        margin: 0 auto;
        display: grid;
        grid-template-columns: repeat(6, minmax(135px, 1fr));
        column-gap: rem(16px);
        row-gap: rem(40px);

        @include media-breakpoint-down(lg) {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
        }

        .number-wrapper {
            width: 100%;
            max-width: rem(420px);
            margin: 0 auto;
            text-align: center;
            grid-column: span 2;

            &:nth-child(4) {
                grid-column: 2 / span 2;
            }

            @include media-breakpoint-down(lg) {
                grid-column: span 1;
                min-width: auto;

                &:nth-child(4) {
                    grid-column: span 1;
                }

                &:nth-child(5) {
                    grid-column: 1 / -1;
                    justify-self: center;
                }
            }
        }
    }
}