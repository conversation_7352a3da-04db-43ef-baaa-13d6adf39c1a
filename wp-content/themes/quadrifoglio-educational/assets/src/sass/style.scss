//
//  Style - Include
//

// Variables and Fonts
@import "variables";
@import "fonts";

// Lib

@import "reset";
@import "~bootstrap";
@import "~slick-carousel/slick/slick";
@import "~slick-carousel/slick/slick-theme";

// Function, mixin

@import "functions";
@import "mixin";
@import "media-query";
@import "spacing";

//  Common style

@import "DOM";

//  Components
@import "./components/search-form";
@import "./components/language-switcher";
@import "./components/language-switcher-mobile";
@import "./components/shapes";
@import "./components/offcanvas";
@import "./components/filter-news";
@import "./components/filter-materiali";
@import "./components/loop-bandi-news";
@import "./components/loop-materiali";
@import "./components/loop-realizzazioni";


//  Blocks
@import "./blocks/navbar";
@import "./blocks/hero-home";
@import "./blocks/hero-text";
@import "./blocks/hero-page";
@import "./blocks/hero-ambiente-parent";
@import "./blocks/hero-ambiente-child";
@import "./blocks/hero-categoria-prodotto";
@import "./blocks/hero-bandi-news";
@import "./blocks/hero-realizzazioni";
@import "./blocks/hero-realizzazione";
@import "./blocks/hero-materiali";
@import "./blocks/content-page-columns";
@import "./blocks/content-page-columns-standard";
@import "./blocks/content-page-text";
@import "./blocks/content-page-ambients";
@import "./blocks/content-categoria-prodotto";
@import "./blocks/content-realizzazione";
@import "./blocks/content-realizzazioni";
@import "./blocks/prodotti-realizzazione";
@import "./blocks/product-categories";
@import "./blocks/product-child-ambienti";
@import "./blocks/call-product-categories";
@import "./blocks/products-list";
@import "./blocks/carousel-news";
@import "./blocks/carousel-gallery";
@import "./blocks/video";
@import "./blocks/static-prefooter";
@import "./blocks/form-map";
@import "./blocks/cards";
@import "./blocks/certifications";
@import "./blocks/footer";


//  Pages
@import "./pages/page-contattaci.scss";
@import "./pages/search";
@import "./pages/404";
