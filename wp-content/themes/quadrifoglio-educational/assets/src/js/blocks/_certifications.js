import 'arrive';
import $ from 'jquery';

document.arrive('.block-certifications', { existing: true }, function () {

    const $blockCertifications = $(this);
    const $carousel = $blockCertifications.find('.carousel-certifications');
    const $offcanvas = $blockCertifications.find('#offcanvasExample');
    let carouselInitialized = false;

    // Inizializza il carousel subito (anche se nascosto)
    console.log('DEBUG: Initializing carousel on page load...');
    console.log('DEBUG: Carousel element found:', $carousel.length > 0);
    console.log('DEBUG: Number of slides found:', $carousel.find('.slide-certification').length);

    try {
        $carousel.slick({
            dots: true,
            arrows: true,
            slidesToShow: 1,
            adaptiveHeight: true,
            prevArrow: `
                <button type="button" class="slick-prev" aria-label="Precedente">
                    <svg width="30" height="26" viewBox="0 0 30 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M13.3839 2.63388C13.872 2.14573 13.872 1.35427 13.3839 0.866117C12.8957 0.377961 12.1043 0.377961 11.6161 0.866117L0.366117 12.1161C-0.122039 12.6043 -0.122039 13.3957 0.366117 13.8839L11.6161 25.1339C12.1043 25.622 12.8957 25.622 13.3839 25.1339C13.872 24.6457 13.872 23.8543 13.3839 23.3661L4.26777 14.25H28.75C29.4403 14.25 30 13.6904 30 13C30 12.3096 29.4403 11.75 28.75 11.75H4.26777L13.3839 2.63388Z" fill="black"/>
                    </svg>
                </button>
            `,
            nextArrow: `
                <button type="button" class="slick-next" aria-label="Successivo">
                    <svg width="30" height="26" viewBox="0 0 30 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18.3839 0.866117C17.8957 0.377961 17.1043 0.377961 16.6161 0.866117C16.128 1.35427 16.128 2.14573 16.6161 2.63388L25.7322 11.75H1.25C0.559644 11.75 0 12.3096 0 13C0 13.6904 0.559644 14.25 1.25 14.25H25.7322L16.6161 23.3661C16.128 23.8543 16.128 24.6457 16.6161 25.1339C17.1043 25.622 17.8957 25.622 18.3839 25.1339L29.6339 13.8839C30.122 13.3957 30.122 12.6043 29.6339 12.1161L18.3839 0.866117Z" fill="black"/>
                    </svg>
                </button>
            `,
        });
        carouselInitialized = true;
        console.log('DEBUG: Carousel initialized successfully on page load');
    } catch (error) {
        console.error('DEBUG: Error initializing carousel:', error);
    }

    // Variabile per memorizzare l'indice della slide da mostrare
    let targetSlideIndex = 0;

    // Gestisce il click sulle certificazioni
    $blockCertifications.find('.certification').on('click', function() {
        targetSlideIndex = parseInt($(this).data('slide-index'));
        console.log('DEBUG: Clicked certification with slide index:', targetSlideIndex);
        console.log('DEBUG: Offcanvas element exists:', $offcanvas.length > 0);
        console.log('DEBUG: Offcanvas ID:', $offcanvas.attr('id'));
    });

    // Eventi dell'offcanvas per debug
    $offcanvas.on('show.bs.offcanvas', function() {
        console.log('DEBUG: Offcanvas is about to show');
    });

    $offcanvas.on('shown.bs.offcanvas', function() {
        console.log('DEBUG: Offcanvas shown event triggered, going to slide:', targetSlideIndex);

        // Forza il refresh del carousel per gestire elementi nascosti
        $carousel.slick('setPosition');

        // Aspetta un po' di più e poi naviga
        setTimeout(function() {
            console.log('DEBUG: Navigating to slide:', targetSlideIndex);
            console.log('DEBUG: Carousel has slick-initialized class:', $carousel.hasClass('slick-initialized'));
            console.log('DEBUG: Carousel jQuery object:', $carousel);
            console.log('DEBUG: Number of slides:', $carousel.find('.slide-certification').length);

            try {
                // Prova diversi metodi
                console.log('DEBUG: Trying slickGoTo...');
                $carousel.slick('slickGoTo', targetSlideIndex);
                console.log('DEBUG: slickGoTo completed');
            } catch (error) {
                console.error('DEBUG: Error with slickGoTo:', error);

                // Prova metodo alternativo
                try {
                    console.log('DEBUG: Trying alternative method...');
                    $carousel.slick('slickCurrentSlide', targetSlideIndex);
                } catch (error2) {
                    console.error('DEBUG: Error with alternative method:', error2);
                }
            }

            // Forza un altro refresh dopo la navigazione
            setTimeout(function() {
                $carousel.slick('setPosition');
                console.log('DEBUG: setPosition completed');
            }, 50);
        }, 200);
    });

    $offcanvas.on('hide.bs.offcanvas', function() {
        console.log('DEBUG: Offcanvas is about to hide');
    });

    $offcanvas.on('hidden.bs.offcanvas', function() {
        console.log('DEBUG: Offcanvas hidden');
    });

    // Prova anche l'evento globale
    $(document).on('shown.bs.offcanvas', function(e) {
        console.log('DEBUG: Global offcanvas shown event:', e.target.id);
        if (e.target.id === 'offcanvasExample') {
            console.log('DEBUG: Global event matches our offcanvas');
        }
    });

});
